package com.azkar.models

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "zikr_categories")
data class ZikrCategory(
    @PrimaryKey
    val id: Int,
    val name: String,
    val description: String,
    val iconName: String,
    val colorHex: String,
    val order: Int
)

enum class CategoryType(val id: Int, val nameKey: String, val descKey: String, val icon: String, val color: String) {
    MORNING(1, "morning_azkar", "morning_azkar_desc", "wb_sunny", "#4CAF50"),
    EVENING(2, "evening_azkar", "evening_azkar_desc", "brightness_3", "#FF9800"),
    SLEEP(3, "sleep_azkar", "sleep_azkar_desc", "bedtime", "#9C27B0"),
    PRAYER(4, "prayer_azkar", "prayer_azkar_desc", "mosque", "#2196F3"),
    GENERAL(5, "general_azkar", "general_azkar_desc", "auto_awesome", "#607D8B"),
    QURAN(6, "quran_azkar", "quran_azkar_desc", "menu_book", "#795548")
}
