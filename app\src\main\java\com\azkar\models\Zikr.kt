package com.azkar.models

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey

@Entity(
    tableName = "azkar",
    foreignKeys = [
        ForeignKey(
            entity = ZikrCategory::class,
            parentColumns = ["id"],
            childColumns = ["categoryId"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class Zikr(
    @PrimaryKey(autoGenerate = true)
    val id: Int = 0,
    val categoryId: Int,
    val arabicText: String,
    val transliteration: String? = null,
    val translation: String,
    val reference: String? = null,
    val count: Int = 1,
    val currentCount: Int = 0,
    val order: Int,
    val isFavorite: Boolean = false,
    val benefits: String? = null,
    val isCompleted: Boolean = false
)

data class ZikrWithCategory(
    val zikr: Zikr,
    val category: ZikrCategory
)
