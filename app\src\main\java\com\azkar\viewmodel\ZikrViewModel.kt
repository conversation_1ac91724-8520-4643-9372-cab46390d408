package com.azkar.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.azkar.models.Zikr
import com.azkar.models.ZikrCategory
import com.azkar.repository.ZikrRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class ZikrViewModel(
    private val repository: ZikrRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ZikrUiState())
    val uiState: StateFlow<ZikrUiState> = _uiState.asStateFlow()
    
    private val _selectedCategory = MutableStateFlow<ZikrCategory?>(null)
    val selectedCategory: StateFlow<ZikrCategory?> = _selectedCategory.asStateFlow()
    
    val categories = repository.getAllCategories()
    val favoriteAzkar = repository.getFavoriteAzkar()
    
    fun selectCategory(category: ZikrCategory) {
        _selectedCategory.value = category
        loadAzkarForCategory(category.id)
    }
    
    fun loadAzkarForCategory(categoryId: Int) {
        viewModelScope.launch {
            repository.getAzkarByCategory(categoryId).collect { azkar ->
                _uiState.value = _uiState.value.copy(
                    currentAzkar = azkar,
                    isLoading = false
                )
            }
        }
    }
    
    fun incrementZikrCount(zikrId: Int) {
        viewModelScope.launch {
            repository.incrementZikrCount(zikrId)
            
            // Check if zikr is completed and show message
            val zikr = repository.getZikrById(zikrId)
            zikr?.let {
                if (it.currentCount + 1 >= it.count) {
                    _uiState.value = _uiState.value.copy(
                        showCompletionMessage = true,
                        completedZikrText = it.arabicText
                    )
                }
            }
        }
    }
    
    fun resetZikrCount(zikrId: Int) {
        viewModelScope.launch {
            repository.resetZikrCount(zikrId)
        }
    }
    
    fun toggleFavorite(zikrId: Int, isFavorite: Boolean) {
        viewModelScope.launch {
            repository.updateZikrFavorite(zikrId, !isFavorite)
        }
    }
    
    fun resetCategoryProgress(categoryId: Int) {
        viewModelScope.launch {
            repository.resetCategoryProgress(categoryId)
        }
    }
    
    fun dismissCompletionMessage() {
        _uiState.value = _uiState.value.copy(
            showCompletionMessage = false,
            completedZikrText = ""
        )
    }
    
    fun setLoading(isLoading: Boolean) {
        _uiState.value = _uiState.value.copy(isLoading = isLoading)
    }
}

data class ZikrUiState(
    val currentAzkar: List<Zikr> = emptyList(),
    val isLoading: Boolean = true,
    val showCompletionMessage: Boolean = false,
    val completedZikrText: String = "",
    val error: String? = null
)
