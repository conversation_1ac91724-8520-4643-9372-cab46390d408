package com.azkar.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.azkar.models.Zikr
import com.azkar.models.ZikrCategory

@Database(
    entities = [Zikr::class, ZikrCategory::class],
    version = 1,
    exportSchema = false
)
abstract class ZikrDatabase : RoomDatabase() {
    
    abstract fun zikrDao(): ZikrDao
    
    companion object {
        @Volatile
        private var INSTANCE: ZikrDatabase? = null
        
        fun getDatabase(context: Context): ZikrDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    ZikrDatabase::class.java,
                    "zikr_database"
                )
                .addCallback(DatabaseInitializer(context))
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
