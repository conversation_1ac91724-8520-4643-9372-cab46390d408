package com.azkar.repository

import com.azkar.database.ZikrDao
import com.azkar.models.Zikr
import com.azkar.models.ZikrCategory
import com.azkar.models.ZikrWithCategory
import kotlinx.coroutines.flow.Flow

class ZikrRepository(
    private val zikrDao: ZikrDao
) {

    // Categories
    fun getAllCategories(): Flow<List<ZikrCategory>> = zikrDao.getAllCategories()

    suspend fun getCategoryById(categoryId: Int): ZikrCategory? =
        zikrDao.getCategoryById(categoryId)

    // Azkar
    fun getAzkarByCategory(categoryId: Int): Flow<List<Zikr>> =
        zikrDao.getAzkarByCategory(categoryId)

    fun getFavoriteAzkar(): Flow<List<Zikr>> = zikrDao.getFavoriteAzkar()

    suspend fun getZikrById(zikrId: Int): Zikr? = zikrDao.getZikrById(zikrId)

    fun getAzkarWithCategoryByCategory(categoryId: Int): Flow<List<Zikr>> =
        zikrDao.getAzkarWithCategoryByCategory(categoryId)

    // Updates
    suspend fun updateZikr(zikr: Zikr) = zikrDao.updateZikr(zikr)

    suspend fun updateZikrCount(zikrId: Int, count: Int) =
        zikrDao.updateZikrCount(zikrId, count)

    suspend fun updateZikrFavorite(zikrId: Int, isFavorite: Boolean) =
        zikrDao.updateZikrFavorite(zikrId, isFavorite)

    suspend fun updateZikrCompleted(zikrId: Int, isCompleted: Boolean) =
        zikrDao.updateZikrCompleted(zikrId, isCompleted)

    suspend fun incrementZikrCount(zikrId: Int) {
        val zikr = getZikrById(zikrId)
        zikr?.let {
            val newCount = it.currentCount + 1
            val isCompleted = newCount >= it.count
            updateZikrCount(zikrId, newCount)
            if (isCompleted) {
                updateZikrCompleted(zikrId, true)
            }
        }
    }

    suspend fun resetZikrCount(zikrId: Int) {
        updateZikrCount(zikrId, 0)
        updateZikrCompleted(zikrId, false)
    }

    suspend fun resetCategoryProgress(categoryId: Int) =
        zikrDao.resetCategoryProgress(categoryId)

    suspend fun resetAllProgress() = zikrDao.resetAllProgress()

    // Statistics
    fun getCompletedAzkarCount(): Flow<Int> = zikrDao.getCompletedAzkarCount()

    fun getCompletedAzkarCountByCategory(categoryId: Int): Flow<Int> =
        zikrDao.getCompletedAzkarCountByCategory(categoryId)

    fun getTotalAzkarCountByCategory(categoryId: Int): Flow<Int> =
        zikrDao.getTotalAzkarCountByCategory(categoryId)

    suspend fun getCategoryProgress(categoryId: Int): Float {
        val completed = zikrDao.getCompletedAzkarCountByCategory(categoryId)
        val total = zikrDao.getTotalAzkarCountByCategory(categoryId)
        // Note: This is a simplified version. In real implementation,
        // you might want to collect these flows properly
        return 0f // Placeholder
    }
}
