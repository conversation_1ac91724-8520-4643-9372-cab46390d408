package com.azkar.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.azkar.models.CategoryType
import com.azkar.models.ZikrCategory

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    categories: List<ZikrCategory>,
    onCategoryClick: (ZikrCategory) -> Unit,
    onFavoritesClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "أذكار المسلم",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
                IconButton(onClick = onFavoritesClick) {
                    Icon(
                        imageVector = Icons.Filled.Favorite,
                        contentDescription = "المفضلة",
                        tint = Color.Red
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer,
                titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
            )
        )
        
        // Welcome message
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Filled.AutoAwesome,
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    tint = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "بسم الله نبدأ",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Text(
                    text = "اختر فئة الأذكار التي تريد قراءتها",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }
        }
        
        // Categories grid
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(categories) { category ->
                CategoryCard(
                    category = category,
                    onClick = { onCategoryClick(category) }
                )
            }
        }
    }
}

@Composable
fun CategoryCard(
    category: ZikrCategory,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val categoryType = CategoryType.values().find { it.id == category.id }
    val backgroundColor = Color(android.graphics.Color.parseColor(category.colorHex))
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor.copy(alpha = 0.1f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Icon
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(backgroundColor.copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = getCategoryIcon(categoryType),
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    tint = backgroundColor
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = getCategoryDisplayName(categoryType),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = getCategoryDescription(categoryType),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Arrow
            Icon(
                imageVector = Icons.Filled.ArrowForward,
                contentDescription = "فتح",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun getCategoryIcon(categoryType: CategoryType?) = when (categoryType) {
    CategoryType.MORNING -> Icons.Filled.WbSunny
    CategoryType.EVENING -> Icons.Filled.Brightness3
    CategoryType.SLEEP -> Icons.Filled.Bedtime
    CategoryType.PRAYER -> Icons.Filled.Place // Using Place as mosque alternative
    CategoryType.GENERAL -> Icons.Filled.AutoAwesome
    CategoryType.QURAN -> Icons.Filled.MenuBook
    null -> Icons.Filled.Circle
}

@Composable
private fun getCategoryDisplayName(categoryType: CategoryType?) = when (categoryType) {
    CategoryType.MORNING -> "أذكار الصباح"
    CategoryType.EVENING -> "أذكار المساء"
    CategoryType.SLEEP -> "أذكار النوم"
    CategoryType.PRAYER -> "أذكار الصلاة"
    CategoryType.GENERAL -> "أذكار عامة"
    CategoryType.QURAN -> "أذكار من القرآن"
    null -> "فئة غير معروفة"
}

@Composable
private fun getCategoryDescription(categoryType: CategoryType?) = when (categoryType) {
    CategoryType.MORNING -> "أذكار الصباح من السنة النبوية"
    CategoryType.EVENING -> "أذكار المساء من السنة النبوية"
    CategoryType.SLEEP -> "أذكار ما قبل النوم"
    CategoryType.PRAYER -> "أذكار ما بعد الصلاة"
    CategoryType.GENERAL -> "أذكار متنوعة للمسلم"
    CategoryType.QURAN -> "أذكار مأخوذة من القرآن الكريم"
    null -> ""
}
