package com.azkar

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.rememberNavController
import com.azkar.database.ZikrDatabase
import com.azkar.navigation.AzkarNavGraph
import com.azkar.repository.ZikrRepository
import com.azkar.ui.theme.AzkarTheme
import com.azkar.viewmodel.ZikrViewModel

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // Initialize database and repository
        val database = ZikrDatabase.getDatabase(this)
        val repository = ZikrRepository(database.zikrDao())
        
        setContent {
            AzkarTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()
                    val viewModel: ZikrViewModel = viewModel {
                        ZikrViewModel(repository)
                    }
                    
                    AzkarNavGraph(
                        navController = navController,
                        viewModel = viewModel
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AzkarPreview() {
    AzkarTheme {
        // Preview content
    }
}
