package com.azkar.navigation

import androidx.compose.runtime.*
import androidx.compose.runtime.collectAsState
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.azkar.ui.screens.CategoryScreen
import com.azkar.ui.screens.HomeScreen
import com.azkar.viewmodel.ZikrViewModel

sealed class Screen(val route: String) {
    object Home : Screen("home")
    object Category : Screen("category/{categoryId}") {
        fun createRoute(categoryId: Int) = "category/$categoryId"
    }
    object Favorites : Screen("favorites")
}

@Composable
fun AzkarNavGraph(
    navController: NavHostController = rememberNavController(),
    viewModel: ZikrViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val selectedCategory by viewModel.selectedCategory.collectAsState()
    val categories by viewModel.categories.collectAsState(initial = emptyList())
    val favoriteAzkar by viewModel.favoriteAzkar.collectAsState(initial = emptyList())
    
    NavHost(
        navController = navController,
        startDestination = Screen.Home.route
    ) {
        composable(Screen.Home.route) {
            HomeScreen(
                categories = categories,
                onCategoryClick = { category ->
                    viewModel.selectCategory(category)
                    navController.navigate(Screen.Category.createRoute(category.id))
                },
                onFavoritesClick = {
                    navController.navigate(Screen.Favorites.route)
                }
            )
        }
        
        composable(Screen.Category.route) { backStackEntry ->
            val categoryId = backStackEntry.arguments?.getString("categoryId")?.toIntOrNull()
            
            LaunchedEffect(categoryId) {
                categoryId?.let { id ->
                    viewModel.loadAzkarForCategory(id)
                }
            }
            
            CategoryScreen(
                category = selectedCategory,
                azkar = uiState.currentAzkar,
                onBackClick = {
                    navController.popBackStack()
                },
                onZikrCountIncrement = { zikrId ->
                    viewModel.incrementZikrCount(zikrId)
                },
                onZikrFavoriteToggle = { zikrId, isFavorite ->
                    viewModel.toggleFavorite(zikrId, isFavorite)
                },
                onZikrReset = { zikrId ->
                    viewModel.resetZikrCount(zikrId)
                },
                onResetAll = {
                    selectedCategory?.let { category ->
                        viewModel.resetCategoryProgress(category.id)
                    }
                },
                showCompletionMessage = uiState.showCompletionMessage,
                completedZikrText = uiState.completedZikrText,
                onDismissCompletionMessage = {
                    viewModel.dismissCompletionMessage()
                }
            )
        }
        
        composable(Screen.Favorites.route) {
            // Create a fake category for favorites
            val favoritesCategory = com.azkar.models.ZikrCategory(
                id = -1,
                name = "المفضلة",
                description = "الأذكار المفضلة لديك",
                iconName = "favorite",
                colorHex = "#E91E63",
                order = 0
            )
            
            CategoryScreen(
                category = favoritesCategory,
                azkar = favoriteAzkar,
                onBackClick = {
                    navController.popBackStack()
                },
                onZikrCountIncrement = { zikrId ->
                    viewModel.incrementZikrCount(zikrId)
                },
                onZikrFavoriteToggle = { zikrId, isFavorite ->
                    viewModel.toggleFavorite(zikrId, isFavorite)
                },
                onZikrReset = { zikrId ->
                    viewModel.resetZikrCount(zikrId)
                },
                onResetAll = {
                    // Reset all favorite azkar
                    favoriteAzkar.forEach { zikr ->
                        viewModel.resetZikrCount(zikr.id)
                    }
                },
                showCompletionMessage = uiState.showCompletionMessage,
                completedZikrText = uiState.completedZikrText,
                onDismissCompletionMessage = {
                    viewModel.dismissCompletionMessage()
                }
            )
        }
    }
}
