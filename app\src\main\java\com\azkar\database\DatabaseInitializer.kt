package com.azkar.database

import android.content.Context
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import com.azkar.models.CategoryType
import com.azkar.models.Zikr
import com.azkar.models.ZikrCategory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class DatabaseInitializer(private val context: Context) : RoomDatabase.Callback() {
    
    override fun onCreate(db: SupportSQLiteDatabase) {
        super.onCreate(db)
        CoroutineScope(Dispatchers.IO).launch {
            populateDatabase()
        }
    }
    
    private suspend fun populateDatabase() {
        val database = ZikrDatabase.getDatabase(context)
        val dao = database.zikrDao()
        
        // Insert categories
        val categories = CategoryType.values().map { categoryType ->
            ZikrCategory(
                id = categoryType.id,
                name = categoryType.nameKey,
                description = categoryType.descKey,
                iconName = categoryType.icon,
                colorHex = categoryType.color,
                order = categoryType.id
            )
        }
        dao.insertCategories(categories)
        
        // Insert sample azkar
        val azkar = getSampleAzkar()
        dao.insertAzkar(azkar)
    }
    
    private fun getSampleAzkar(): List<Zikr> {
        return listOf(
            // أذكار الصباح
            Zikr(
                categoryId = CategoryType.MORNING.id,
                arabicText = "أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ",
                transliteration = "Asbahna wa asbahal-mulku lillah, walhamdu lillah, la ilaha illa Allah wahdahu la shareeka lah, lahul-mulku walahul-hamd, wa huwa 'ala kulli shay'in qadeer",
                translation = "أصبحنا وأصبح الملك لله، والحمد لله، لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير",
                reference = "مسلم",
                count = 1,
                order = 1,
                benefits = "حفظ من الشيطان والشر"
            ),
            Zikr(
                categoryId = CategoryType.MORNING.id,
                arabicText = "اللَّهُمَّ بِكَ أَصْبَحْنَا، وَبِكَ أَمْسَيْنَا، وَبِكَ نَحْيَا، وَبِكَ نَمُوتُ، وَإِلَيْكَ النُّشُورُ",
                transliteration = "Allahumma bika asbahna, wa bika amsayna, wa bika nahya, wa bika namootu, wa ilaykan-nushoor",
                translation = "اللهم بك أصبحنا، وبك أمسينا، وبك نحيا، وبك نموت، وإليك النشور",
                reference = "الترمذي",
                count = 1,
                order = 2,
                benefits = "تفويض الأمر إلى الله"
            ),
            Zikr(
                categoryId = CategoryType.MORNING.id,
                arabicText = "سُبْحَانَ اللَّهِ وَبِحَمْدِهِ",
                transliteration = "Subhan Allah wa bihamdih",
                translation = "سبحان الله وبحمده",
                reference = "البخاري ومسلم",
                count = 100,
                order = 3,
                benefits = "غرس نخلة في الجنة"
            ),
            
            // أذكار المساء
            Zikr(
                categoryId = CategoryType.EVENING.id,
                arabicText = "أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ",
                transliteration = "Amsayna wa amsal-mulku lillah, walhamdu lillah, la ilaha illa Allah wahdahu la shareeka lah, lahul-mulku walahul-hamd, wa huwa 'ala kulli shay'in qadeer",
                translation = "أمسينا وأمسى الملك لله، والحمد لله، لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير",
                reference = "مسلم",
                count = 1,
                order = 1,
                benefits = "حفظ من الشيطان والشر"
            ),
            
            // أذكار النوم
            Zikr(
                categoryId = CategoryType.SLEEP.id,
                arabicText = "بِاسْمِكَ اللَّهُمَّ أَمُوتُ وَأَحْيَا",
                transliteration = "Bismika Allahumma amootu wa ahya",
                translation = "باسمك اللهم أموت وأحيا",
                reference = "البخاري",
                count = 1,
                order = 1,
                benefits = "حفظ أثناء النوم"
            ),
            
            // أذكار الصلاة
            Zikr(
                categoryId = CategoryType.PRAYER.id,
                arabicText = "سُبْحَانَ اللَّهِ",
                transliteration = "Subhan Allah",
                translation = "سبحان الله",
                reference = "البخاري ومسلم",
                count = 33,
                order = 1,
                benefits = "أجر عظيم بعد الصلاة"
            ),
            Zikr(
                categoryId = CategoryType.PRAYER.id,
                arabicText = "الْحَمْدُ لِلَّهِ",
                transliteration = "Alhamdulillah",
                translation = "الحمد لله",
                reference = "البخاري ومسلم",
                count = 33,
                order = 2,
                benefits = "أجر عظيم بعد الصلاة"
            ),
            Zikr(
                categoryId = CategoryType.PRAYER.id,
                arabicText = "اللَّهُ أَكْبَرُ",
                transliteration = "Allahu Akbar",
                translation = "الله أكبر",
                reference = "البخاري ومسلم",
                count = 34,
                order = 3,
                benefits = "أجر عظيم بعد الصلاة"
            )
        )
    }
}
