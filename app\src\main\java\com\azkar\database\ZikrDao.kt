package com.azkar.database

import androidx.room.*
import com.azkar.models.Zikr
import com.azkar.models.ZikrCategory
import com.azkar.models.ZikrWithCategory
import kotlinx.coroutines.flow.Flow

@Dao
interface ZikrDao {

    // Category operations
    @Query("SELECT * FROM zikr_categories ORDER BY `order`")
    fun getAllCategories(): Flow<List<ZikrCategory>>

    @Query("SELECT * FROM zikr_categories WHERE id = :categoryId")
    suspend fun getCategoryById(categoryId: Int): ZikrCategory?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategories(categories: List<ZikrCategory>)

    // Zikr operations
    @Query("SELECT * FROM azkar WHERE categoryId = :categoryId ORDER BY `order`")
    fun getAzkarByCategory(categoryId: Int): Flow<List<Zikr>>

    @Query("SELECT * FROM azkar WHERE isFavorite = 1 ORDER BY `order`")
    fun getFavoriteAzkar(): Flow<List<Zikr>>

    @Query("SELECT * FROM azkar WHERE id = :zikrId")
    suspend fun getZikrById(zikrId: Int): Zikr?

    @Query("SELECT * FROM azkar WHERE categoryId = :categoryId ORDER BY `order`")
    fun getAzkarWithCategoryByCategory(categoryId: Int): Flow<List<Zikr>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAzkar(azkar: List<Zikr>)

    @Update
    suspend fun updateZikr(zikr: Zikr)

    @Query("UPDATE azkar SET currentCount = :count WHERE id = :zikrId")
    suspend fun updateZikrCount(zikrId: Int, count: Int)

    @Query("UPDATE azkar SET isFavorite = :isFavorite WHERE id = :zikrId")
    suspend fun updateZikrFavorite(zikrId: Int, isFavorite: Boolean)

    @Query("UPDATE azkar SET isCompleted = :isCompleted WHERE id = :zikrId")
    suspend fun updateZikrCompleted(zikrId: Int, isCompleted: Boolean)

    @Query("UPDATE azkar SET currentCount = 0, isCompleted = 0 WHERE categoryId = :categoryId")
    suspend fun resetCategoryProgress(categoryId: Int)

    @Query("UPDATE azkar SET currentCount = 0, isCompleted = 0")
    suspend fun resetAllProgress()

    // Statistics
    @Query("SELECT COUNT(*) FROM azkar WHERE isCompleted = 1")
    fun getCompletedAzkarCount(): Flow<Int>

    @Query("SELECT COUNT(*) FROM azkar WHERE categoryId = :categoryId AND isCompleted = 1")
    fun getCompletedAzkarCountByCategory(categoryId: Int): Flow<Int>

    @Query("SELECT COUNT(*) FROM azkar WHERE categoryId = :categoryId")
    fun getTotalAzkarCountByCategory(categoryId: Int): Flow<Int>
}
